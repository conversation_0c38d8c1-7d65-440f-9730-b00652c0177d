#!/usr/bin/env python3
"""
Simple redirect server to redirect port 8080 to 8081
This helps users who are still accessing the old port
"""

from flask import Flask, redirect
import sys

app = Flask(__name__)

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def redirect_to_8081(path):
    """Redirect all requests from port 8080 to port 8081"""
    if path:
        return redirect(f'http://localhost:8081/{path}', code=301)
    else:
        return redirect('http://localhost:8081/', code=301)

if __name__ == '__main__':
    print("Starting redirect server on port 8080...")
    print("All requests will be redirected to port 8081")
    try:
        app.run(host='0.0.0.0', port=8080, debug=False)
    except OSError as e:
        if "Address already in use" in str(e):
            print("Port 8080 is already in use. Please close any existing servers on port 8080.")
            sys.exit(1)
        else:
            raise
